#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对冲基金LLM模型性能分析脚本
分析不同LLM模型在股票交易代理中的表现
"""

import json
import os
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from collections import defaultdict
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
import sys

# 添加项目路径以导入回测系统的价格获取功能
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class LLMPerformanceAnalyzer:
    def __init__(self):
        self.data = {}
        self.models = ['gemini2.0_flash', 'gpt3.5', 'grok_beta']
        self.stocks = ['AAPL', 'MSFT', 'NVDA']
        
        # 文件路径映射
        self.file_paths = {
            'AAPL': {
                'gemini2.0_flash': 'reasoning_logs/accuracy_tracking_AAPL_20250101-20250601_gemini2.0_flash/final_accuracy_report_experiment_2025-07-03_AAPL.json',
                'gpt3.5': 'reasoning_logs/accuracy_tracking_AAPL_20250101-20250601_gpt3.5/final_accuracy_report_experiment_2025-07-02_AAPL.json',
                'grok_beta': 'reasoning_logs/accuracy_tracking_AAPL_20250101-20250601_grok_beta/final_accuracy_report_experiment_2025-07-03_AAPL.json'
            },
            'MSFT': {
                'gemini2.0_flash': 'reasoning_logs/accuracy_tracking_MSFT_20250101-20250601_gemini2.0_flash/final_accuracy_report_experiment_2025-07-06_MSFT.json',
                'gpt3.5': 'reasoning_logs/accuracy_tracking_MSFT_20250101-20250601_gpt3.5/final_accuracy_report_experiment_2025-07-06_MSFT.json',
                'grok_beta': 'reasoning_logs/accuracy_tracking_MSFT_20250101-20250601_grok_beta/final_accuracy_report_experiment_2025-07-07_MSFT.json'
            },
            'NVDA': {
                'gemini2.0_flash': 'reasoning_logs/accuracy_tracking_NVDA_20250101-20250601_gemini2.0_flash/final_accuracy_report_experiment_2025-06-26_NVDA.json',
                'gpt3.5': 'reasoning_logs/accuracy_tracking_NVDA_20250101-20250601_gpt3.5/final_accuracy_report_experiment_2025-06-26_NVDA.json',
                'grok_beta': 'reasoning_logs/accuracy_tracking_NVDA_20250101-20250601_grok-beta/final_accuracy_report_experiment_2025-06-28_NVDA.json'
            }
        }
    
    def load_data(self):
        """加载所有JSON数据文件"""
        print("正在加载数据...")

        for stock in self.stocks:
            self.data[stock] = {}
            for model in self.models:
                file_path = self.file_paths[stock][model]
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # 处理JSON文件末尾的注释
                        lines = content.split('\n')
                        json_lines = []
                        for line in lines:
                            if line.strip().startswith('//') or line.strip().startswith('#'):
                                break
                            json_lines.append(line)

                        clean_content = '\n'.join(json_lines)
                        data = json.loads(clean_content)
                        self.data[stock][model] = data['cumulative_stats']
                    print(f"✅ 成功加载 {stock} - {model}")
                except FileNotFoundError:
                    print(f"❌ 文件未找到: {file_path}")
                    self.data[stock][model] = {}
                except Exception as e:
                    print(f"❌ 加载 {stock} - {model} 时出错: {e}")
                    self.data[stock][model] = {}
    
    def get_agent_names(self):
        """获取所有代理名称"""
        agents = set()
        for stock in self.stocks:
            for model in self.models:
                if self.data[stock][model]:
                    agents.update(self.data[stock][model].keys())
        return sorted(list(agents))
    
    def create_accuracy_comparison_chart(self, stock):
        """创建准确率对比图表"""
        agents = self.get_agent_names()
        
        # 准备数据
        accuracy_data = {}
        for model in self.models:
            accuracy_data[model] = []
            for agent in agents:
                if agent in self.data[stock][model]:
                    accuracy = self.data[stock][model][agent].get('accuracy_rate', 0)
                    accuracy_data[model].append(accuracy * 100)  # 转换为百分比
                else:
                    accuracy_data[model].append(0)
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(16, 10))
        
        x = np.arange(len(agents))
        width = 0.25
        
        # 绘制条形图
        bars1 = ax.bar(x - width, accuracy_data['gemini2.0_flash'], width, 
                      label='Gemini 2.0 Flash', alpha=0.8, color='#1f77b4')
        bars2 = ax.bar(x, accuracy_data['gpt3.5'], width, 
                      label='GPT-3.5', alpha=0.8, color='#ff7f0e')
        bars3 = ax.bar(x + width, accuracy_data['grok_beta'], width, 
                      label='Grok Beta', alpha=0.8, color='#2ca02c')
        
        # 设置图表属性
        ax.set_xlabel('交易代理', fontsize=12)
        ax.set_ylabel('准确率 (%)', fontsize=12)
        ax.set_title(f'{stock} 股票 - 不同LLM模型代理准确率对比', fontsize=16, fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels([agent.replace('_', ' ').title() for agent in agents], 
                          rotation=45, ha='right')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        def add_value_labels(bars):
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                           f'{height:.1f}%', ha='center', va='bottom', fontsize=8)
        
        add_value_labels(bars1)
        add_value_labels(bars2)
        add_value_labels(bars3)
        
        plt.tight_layout()
        return fig
    
    def create_signal_distribution_chart(self, stock):
        """创建信号分布对比图表"""
        agents = self.get_agent_names()
        
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle(f'{stock} 股票 - 不同LLM模型信号分布对比', fontsize=18, fontweight='bold')
        
        signal_types = ['bullish', 'bearish', 'neutral']
        colors = {'bullish': '#2ca02c', 'bearish': '#d62728', 'neutral': '#ff7f0e'}
        
        # 为每个模型创建子图
        model_positions = [(0, 0), (0, 1), (1, 0)]
        
        for idx, model in enumerate(self.models):
            ax = axes[model_positions[idx][0], model_positions[idx][1]]
            
            # 准备数据
            signal_data = {signal: [] for signal in signal_types}
            agent_labels = []
            
            for agent in agents:
                if agent in self.data[stock][model]:
                    agent_stats = self.data[stock][model][agent].get('signal_stats', {})
                    agent_labels.append(agent.replace('_', ' ').title())
                    
                    for signal in signal_types:
                        total = agent_stats.get(signal, {}).get('total', 0)
                        signal_data[signal].append(total)
                else:
                    agent_labels.append(agent.replace('_', ' ').title())
                    for signal in signal_types:
                        signal_data[signal].append(0)
            
            # 创建堆叠条形图
            x = np.arange(len(agents))
            bottom = np.zeros(len(agents))
            
            for signal in signal_types:
                ax.bar(x, signal_data[signal], bottom=bottom, 
                      label=f'{signal.title()} 信号', color=colors[signal], alpha=0.8)
                bottom += signal_data[signal]
            
            ax.set_title(f'{model.replace("_", " ").title()} 模型', fontsize=14, fontweight='bold')
            ax.set_xlabel('交易代理', fontsize=12)
            ax.set_ylabel('信号数量', fontsize=12)
            ax.set_xticks(x)
            ax.set_xticklabels(agent_labels, rotation=45, ha='right')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # 在第四个子图位置添加股票价格走势图
        self.add_price_trend_chart(axes[1, 1], stock)

        plt.tight_layout()
        return fig

    def add_price_trend_chart(self, ax, stock):
        """在指定的子图中添加股票价格走势图"""
        try:
            # 使用yfinance获取2025年1月1日到6月1日的价格数据
            start_date = "2025-01-01"
            end_date = "2025-06-01"

            # 获取股票数据
            ticker = yf.Ticker(stock)
            price_data = ticker.history(start=start_date, end=end_date)

            if price_data.empty:
                # 如果没有数据，显示提示信息
                ax.text(0.5, 0.5, f'无法获取 {stock} 价格数据\n({start_date} 至 {end_date})',
                       ha='center', va='center', transform=ax.transAxes, fontsize=12)
                ax.set_title(f'{stock} 价格走势', fontsize=12, fontweight='bold')
                return

            # 绘制收盘价走势
            ax.plot(price_data.index, price_data['Close'],
                   color='#1f77b4', linewidth=2, label='收盘价')

            # 添加移动平均线
            if len(price_data) >= 20:
                ma20 = price_data['Close'].rolling(window=20).mean()
                ax.plot(price_data.index, ma20,
                       color='#ff7f0e', linewidth=1.5, alpha=0.8, label='20日均线')

            # 设置图表样式
            ax.set_title(f'{stock} 价格走势 (2025年)', fontsize=12, fontweight='bold')
            ax.set_xlabel('日期', fontsize=10)
            ax.set_ylabel('价格 (USD)', fontsize=10)
            ax.legend(fontsize=9)
            ax.grid(True, alpha=0.3)

            # 格式化x轴日期显示
            ax.tick_params(axis='x', rotation=45, labelsize=8)
            ax.tick_params(axis='y', labelsize=8)

        except Exception as e:
            # 如果获取数据失败，显示错误信息
            ax.text(0.5, 0.5, f'获取 {stock} 价格数据失败\n错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, fontsize=10)
            ax.set_title(f'{stock} 价格走势', fontsize=12, fontweight='bold')
    
    def generate_summary_report(self):
        """生成汇总报告"""
        print("\n" + "="*80)
        print("LLM模型性能分析汇总报告")
        print("="*80)
        
        for stock in self.stocks:
            print(f"\n📊 {stock} 股票分析结果:")
            print("-" * 50)
            
            agents = self.get_agent_names()
            
            # 计算每个模型的平均准确率
            model_avg_accuracy = {}
            for model in self.models:
                accuracies = []
                for agent in agents:
                    if agent in self.data[stock][model]:
                        accuracy = self.data[stock][model][agent].get('accuracy_rate', 0)
                        accuracies.append(accuracy)
                
                if accuracies:
                    model_avg_accuracy[model] = np.mean(accuracies) * 100
                else:
                    model_avg_accuracy[model] = 0
            
            # 排序并显示结果
            sorted_models = sorted(model_avg_accuracy.items(), key=lambda x: x[1], reverse=True)
            
            for rank, (model, avg_acc) in enumerate(sorted_models, 1):
                print(f"  {rank}. {model}: {avg_acc:.2f}% 平均准确率")
            
            # 找出表现最好的代理
            best_agent_performance = {}
            for agent in agents:
                agent_accuracies = {}
                for model in self.models:
                    if agent in self.data[stock][model]:
                        accuracy = self.data[stock][model][agent].get('accuracy_rate', 0)
                        agent_accuracies[model] = accuracy * 100
                
                if agent_accuracies:
                    best_model = max(agent_accuracies.items(), key=lambda x: x[1])
                    best_agent_performance[agent] = best_model
            
            print(f"\n  🏆 各代理最佳表现模型:")
            for agent, (model, accuracy) in best_agent_performance.items():
                print(f"    {agent}: {model} ({accuracy:.2f}%)")
    
    def run_analysis(self):
        """运行完整分析"""
        self.load_data()
        
        print("\n开始生成图表...")
        
        # 为每个股票生成图表
        for stock in self.stocks:
            print(f"\n正在生成 {stock} 的分析图表...")
            
            # 准确率对比图
            fig1 = self.create_accuracy_comparison_chart(stock)
            fig1.savefig(f'{stock}_accuracy_comparison.png', dpi=300, bbox_inches='tight')
            print(f"✅ 保存准确率对比图: {stock}_accuracy_comparison.png")
            
            # 信号分布图
            fig2 = self.create_signal_distribution_chart(stock)
            fig2.savefig(f'{stock}_signal_distribution.png', dpi=300, bbox_inches='tight')
            print(f"✅ 保存信号分布图: {stock}_signal_distribution.png")
            
            plt.close(fig1)
            plt.close(fig2)
        
        # 生成汇总报告
        self.generate_summary_report()
        
        print(f"\n🎉 分析完成！共生成了 {len(self.stocks) * 2} 张图表")
        print("📁 图表文件已保存在当前目录中")

if __name__ == "__main__":
    analyzer = LLMPerformanceAnalyzer()
    analyzer.run_analysis()
